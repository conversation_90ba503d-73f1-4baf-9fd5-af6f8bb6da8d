const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'training_system',
  user: 'training_user',
  password: 'training_password',
};

async function resetPassword() {
  const pool = new Pool(dbConfig);
  
  try {
    // Generate new hash for admin123
    const password = 'admin123';
    const saltRounds = 12;
    const newHash = await bcrypt.hash(password, saltRounds);
    
    console.log('Generated new hash for password "admin123":', newHash);
    
    // Test the new hash
    const testResult = await bcrypt.compare(password, newHash);
    console.log('New hash verification test:', testResult);
    
    // Update the database
    const result = await pool.query(
      'UPDATE users SET password_hash = $1 WHERE email = $2',
      [newHash, '<EMAIL>']
    );
    
    console.log('Database update result:', result.rowCount, 'rows updated');
    
    // Verify the update
    const verifyResult = await pool.query(
      'SELECT email, password_hash FROM users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (verifyResult.rows.length > 0) {
      const storedHash = verifyResult.rows[0].password_hash;
      const verifyTest = await bcrypt.compare(password, storedHash);
      console.log('Database verification test:', verifyTest);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
  }
}

resetPassword();
