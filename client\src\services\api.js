import axios from 'axios'

// Create axios instance with default config
const api = axios.create({
  baseURL: 'http://localhost:3002/api',
  withCredentials: true,
  timeout: 30000,
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.baseURL)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Redirect to login on unauthorized
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getMe: () => api.get('/auth/me'),
  checkStatus: () => api.get('/auth/status'),
  changePassword: (data) => api.post('/auth/change-password', data),
}

// Users API
export const usersAPI = {
  getAll: () => api.get('/users'),
  getProfile: () => api.get('/users/profile'),
  bulkCreate: (emails) => api.post('/users/bulk-create', { emails }),
  update: (email, data) => api.put(`/users/${email}`, data),
  resetPassword: (email) => api.post(`/users/${email}/reset-password`),
  delete: (email) => api.delete(`/users/${email}`),
}

// Training API
export const trainingAPI = {
  getModules: () => api.get('/training/modules'),
  getModule: (id) => api.get(`/training/modules/${id}`),
  getProgress: (moduleId) => api.get(`/training/modules/${moduleId}/progress`),
  updateProgress: (moduleId, data) => api.post(`/training/modules/${moduleId}/progress`, data),
  createModule: (data) => api.post('/training/modules', data),
  uploadVideo: (moduleId, formData) => api.post(`/training/modules/${moduleId}/videos`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 300000, // 5 minutes for video upload
  }),
}

// Quiz API
export const quizAPI = {
  getQuiz: (moduleId, languageCode) => api.get(`/quiz/modules/${moduleId}/quiz/${languageCode}`),
  submitQuiz: (moduleId, languageCode, answers) => api.post(`/quiz/modules/${moduleId}/quiz/${languageCode}/submit`, { answers }),
  createQuiz: (data) => api.post('/quiz/create', data),
}

// Reports API
export const reportsAPI = {
  getSOCReport: (params) => api.get('/reports/soc-compliance', { params }),
  getCompletionStats: (params) => api.get('/reports/completion-stats', { params }),
  getUserActivity: (params) => api.get('/reports/user-activity', { params }),
  downloadSOCReport: (params) => {
    return api.get('/reports/soc-compliance', { 
      params: { ...params, format: 'html' },
      responseType: 'blob'
    })
  },
}

// Helper function to handle API errors
export const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    const message = error.response.data?.error || error.response.data?.message || 'An error occurred'
    return {
      message,
      status: error.response.status,
      details: error.response.data?.details,
    }
  } else if (error.request) {
    // Request was made but no response received
    return {
      message: 'Network error - please check your connection',
      status: 0,
    }
  } else {
    // Something else happened
    return {
      message: error.message || 'An unexpected error occurred',
      status: 0,
    }
  }
}

// Helper function to download file from blob response
export const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

export default api
