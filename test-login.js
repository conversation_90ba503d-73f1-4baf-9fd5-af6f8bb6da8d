const axios = require('axios');

async function testLogin() {
  try {
    const response = await axios.post('http://localhost:3002/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Login successful:', response.data);
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
  }
}

testLogin();
