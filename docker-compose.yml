version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: training_postgres
    environment:
      POSTGRES_DB: training_system
      POSTGRES_USER: training_user
      POSTGRES_PASSWORD: training_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - training_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U training_user -d training_system"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: training_backend
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: training_system
      DB_USER: training_user
      DB_PASSWORD: training_password
      PORT: 3002
      SESSION_SECRET: docker_session_secret_change_in_production
    ports:
      - "3002:3002"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./server/uploads:/app/uploads
    networks:
      - training_network
    restart: unless-stopped

  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: training_frontend
    ports:
      - "3001:3001"
    depends_on:
      - backend
    networks:
      - training_network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  training_network:
    driver: bridge
